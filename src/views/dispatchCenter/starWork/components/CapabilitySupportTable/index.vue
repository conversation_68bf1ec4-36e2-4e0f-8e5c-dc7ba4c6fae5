<template>
  <div class="capability-table-container" style="margin-top: 20px;">
    <el-table :data="tableData" :empty-text="'暂无数据'" :cell-style="{ textAlign: 'center' }"
      :header-cell-style="{ textAlign: 'center', backgroundColor: '#f5f7fa' }" border style="width: 100%;"
      class="capability-table">

      <!-- 能力方列 -->
      <el-table-column prop="company" label="能力方">
        <template #default="scope">
          <div class="capability-info">
            <span>{{ scope.row.company }}</span>
            <span>{{ scope.row.contact }}</span>
            <span>{{ scope.row.phone }}</span>
          </div>
        </template>
      </el-table-column>

      <!-- 是否支撑列 -->
      <el-table-column prop="isSupport" label="是否支撑" width="120">
        <template #default="scope">
          <span>
            {{ scope.row.isSupport }}
          </span>
        </template>
      </el-table-column>

      <!-- 支撑结果列 -->
      <el-table-column prop="supportResult" label="支撑结果" width="150">
        <template #default="scope">
          <span class="support-result" :title="scope.row.supportResult">
            {{ scope.row.supportResult }}
          </span>
        </template>
      </el-table-column>

      <!-- 流程节点列 -->
      <el-table-column prop="flowNode" label="流程节点" width="120">
        <template #default="scope">
          <span class="flow-node">{{ scope.row.flowNode }}</span>
        </template>
      </el-table-column>

      <!-- 回复时间列 -->
      <el-table-column prop="replyTime" label="回复时间" width="120">
        <template #default="scope">
          <span class="reply-time">{{ scope.row.replyTime }}</span>
        </template>
      </el-table-column>

      <!-- 操作列 -->
      <el-table-column label="操作" width="150">
        <template #default="scope">
          <div class="action-buttons">
            <a-button v-for="action in scope.row.actions" :key="action" type="link" size="small"
              :class="getActionButtonClass(action)" @click="handleAction(action, scope.row)">
              {{ action }}
            </a-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'CapabilitySupportTable',
  props: {
    // 表格数据
    tableData: {
      type: Array,
      default: () => []
    },
    // 是否显示加载状态
    loading: {
      type: Boolean,
      default: false
    }
  },
  emits: ['action-click'],
  setup(props, { emit }) {
    // 获取操作按钮样式类
    const getActionButtonClass = (action) => {
      const actionClassMap = {
        '去评价评分': 'action-btn action-btn--evaluate',
        '结单': 'action-btn action-btn--close',
        '重新派单': 'action-btn action-btn--reassign'
      }
      return actionClassMap[action] || 'action-btn'
    }

    // 处理操作按钮点击
    const handleAction = (action, row) => {
      emit('action-click', { action, row })
    }

    return {
      getActionButtonClass,
      handleAction
    }
  }
})
</script>

<style lang="scss" scoped>
.capability-table-container {
  .capability-table {
    .capability-info {
      display: flex;
      gap: 8px;
      justify-content: center;
      align-items: center;
      font-weight: 500;
      color: #333;
      margin-bottom: 2px;
    }

    .support-status {
      font-weight: 500;

      &--agree {
        color: #52c41a;
      }

      &--reject {
        color: #ff4d4f;
      }

      &--timeout {
        color: #1890ff;
      }
    }

    .support-result {
      display: inline-block;
      max-width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .flow-node,
    .reply-time {
      color: #666;
    }

    .action-buttons {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      justify-content: center;

      .action-btn {
        padding: 0;
        height: auto;
        line-height: 1.2;

        &--evaluate {
          color: #1890ff;
        }

        &--close {
          color: #1890ff;
        }

        &--reassign {
          color: #1890ff;
        }

        &:hover {
          opacity: 0.8;
        }
      }
    }
  }
}
</style>

<style lang="scss">
/* 全局样式，用于覆盖 Element Plus 默认样式 */
.capability-table-container {
  .capability-table {
    .el-table__header-wrapper {
      th {
        background-color: #f5f7fa !important;
        color: #606266;
        font-weight: 500;
      }
    }

    .el-table__body-wrapper {
      .el-table__row {
        &:hover {
          background-color: #f5f7fa;
        }
      }
    }

    .el-table__cell {
      border-right: 1px solid #ebeef5;

      &:last-child {
        border-right: none;
      }
    }
  }
}
</style>
